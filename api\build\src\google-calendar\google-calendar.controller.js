"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var GoogleCalendarController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleCalendarController = void 0;
const common_1 = require("@nestjs/common");
const google_calendar_service_1 = require("./google-calendar.service");
const swagger_1 = require("@nestjs/swagger");
const user_entity_1 = require("../users/entities/user.entity");
const clinic_user_entity_1 = require("../clinics/entities/clinic-user.entity");
const brand_entity_1 = require("../brands/entities/brand.entity");
const passport_1 = require("@nestjs/passport");
const public_decorator_1 = require("../auth/guards/public.decorator");
const get_login_url_1 = require("../utils/common/get-login-url");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const encryption_service_1 = require("../utils/encryption/encryption.service");
let GoogleCalendarController = GoogleCalendarController_1 = class GoogleCalendarController {
    constructor(googleCalendarService, userRepository, clinicUserRepository, brandRepository, encryptionService) {
        this.googleCalendarService = googleCalendarService;
        this.userRepository = userRepository;
        this.clinicUserRepository = clinicUserRepository;
        this.brandRepository = brandRepository;
        this.encryptionService = encryptionService;
        this.logger = new common_1.Logger(GoogleCalendarController_1.name);
    }
    async getAuthUrl(req) {
        const url = await this.googleCalendarService.getAuthUrl(req.user.id);
        return { url };
    }
    async handleOAuthCallback(code, state, res) {
        var _a;
        if (!code || !state) {
            return res.status(common_1.HttpStatus.BAD_REQUEST).send('Missing code or state');
        }
        try {
            await this.googleCalendarService.handleOAuthCallback(code, state);
            // Dynamically resolve brand slug -> admin portal URL
            let baseAdminUrl = (0, get_login_url_1.getAdminPortalUrl)(); // default
            try {
                const user = await this.userRepository.findOne({ where: { id: state }, relations: ['brand'] });
                // Attempt to resolve brand slug directly from the user entity
                let brandSlug = (_a = user === null || user === void 0 ? void 0 : user.brand) === null || _a === void 0 ? void 0 : _a.slug;
                // Fallback: derive brand slug via ClinicUser relation
                if (!brandSlug) {
                    const clinicUser = await this.clinicUserRepository.findOne({ where: { userId: state } });
                    if (clinicUser === null || clinicUser === void 0 ? void 0 : clinicUser.brandId) {
                        const brand = await this.brandRepository.findOne({ where: { id: clinicUser.brandId } });
                        brandSlug = brand === null || brand === void 0 ? void 0 : brand.slug;
                    }
                }
                if (brandSlug) {
                    baseAdminUrl = (0, get_login_url_1.getAdminPortalUrl)(brandSlug);
                }
            }
            catch (err) {
                this.logger.warn('Could not determine brand slug for OAuth redirect', err);
            }
            // Redirect to a success page in the correct domain
            return res.redirect(`${baseAdminUrl}/profile/hours?google-auth=success`);
        }
        catch (error) {
            this.logger.error('OAuth callback failed', error);
            const baseAdminUrl = (0, get_login_url_1.getAdminPortalUrl)();
            // Redirect to a failure page in the frontend
            return res.redirect(`${baseAdminUrl}/profile/hours?google-auth=failed`);
        }
    }
    async listCalendars(req) {
        return this.googleCalendarService.getUserCalendars(req.user.id);
    }
    async connectCalendar(req, calendarId) {
        return this.googleCalendarService.connectCalendar(req.user.id, calendarId);
    }
    async getConnectionStatus(req) {
        return this.googleCalendarService.getConnectionStatus(req.user.id);
    }
    async disconnectCalendar(req) {
        return this.googleCalendarService.disconnectCalendar(req.user.id);
    }
    async debugManualSync(req) {
        try {
            await this.googleCalendarService.performIncrementalSync(req.user.id);
            return { success: true, message: 'Manual sync triggered successfully' };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    async debugGetCachedEvents(req, startDate, endDate) {
        try {
            const start = startDate ? new Date(startDate) : new Date();
            start.setHours(0, 0, 0, 0);
            const end = endDate ? new Date(endDate) : new Date();
            end.setHours(23, 59, 59, 999);
            const events = await this.googleCalendarService.getEventsForDate(req.user.id, '', // calendarId not used in current implementation
            start, end);
            return {
                success: true,
                events,
                count: events.length,
                dateRange: { start, end }
            };
        }
        catch (error) {
            return { success: false, error: error.message };
        }
    }
    async debugGetSyncInfo(req) {
        return this.googleCalendarService.getDebugSyncInfo(req.user.id);
    }
    async handleWebhook(channelId, resourceId, resourceState, channelToken, channelExpiration, allHeaders) {
        if (!channelId || !resourceId || !resourceState) {
            throw new common_1.UnauthorizedException('Missing Google webhook headers');
        }
        // Security: Validate the webhook token if we have one stored
        try {
            const user = await this.userRepository.findOne({ where: { googleWebhookId: channelId } });
            if (user === null || user === void 0 ? void 0 : user.googleWebhookToken) {
                let expectedToken;
                try {
                    expectedToken = this.encryptionService.decrypt(user.googleWebhookToken);
                }
                catch (decryptErr) {
                    // Token might still be stored in plain text (legacy data)
                    expectedToken = user.googleWebhookToken;
                }
                if (!channelToken || channelToken !== expectedToken) {
                    this.logger.warn(`Invalid webhook token for channel ${channelId}. Provided=${channelToken}, Expected=${expectedToken}`);
                    throw new common_1.UnauthorizedException('Invalid webhook token');
                }
            }
        }
        catch (err) {
            throw err;
        }
        this.logger.log(`🔔 WEBHOOK RECEIVED: channelId=${channelId}, resourceId=${resourceId}, state=${resourceState}, token=${channelToken}, expiration=${channelExpiration}`);
        // Log all headers for debugging
        this.logger.debug('Full webhook headers:', allHeaders);
        // Queue this for processing instead of handling it inline
        this.googleCalendarService.processWebhookNotification(channelId, resourceId, resourceState);
        return { success: true, timestamp: new Date().toISOString() };
    }
};
exports.GoogleCalendarController = GoogleCalendarController;
__decorate([
    (0, common_1.Get)('auth'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get Google Auth URL' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "getAuthUrl", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('auth/callback'),
    (0, swagger_1.ApiOperation)({ summary: 'Handle Google OAuth Callback' }),
    __param(0, (0, common_1.Query)('code')),
    __param(1, (0, common_1.Query)('state')),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "handleOAuthCallback", null);
__decorate([
    (0, common_1.Get)('calendars'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'List User Calendars' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "listCalendars", null);
__decorate([
    (0, common_1.Post)('connect'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Connect to a Calendar' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)('calendarId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "connectCalendar", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get Connection Status' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "getConnectionStatus", null);
__decorate([
    (0, common_1.Delete)('disconnect'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Disconnect from Google Calendar' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "disconnectCalendar", null);
__decorate([
    (0, common_1.Post)('debug/sync'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Debug: Trigger Manual Sync' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "debugManualSync", null);
__decorate([
    (0, common_1.Get)('debug/cached-events'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Debug: Get Cached Events' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "debugGetCachedEvents", null);
__decorate([
    (0, common_1.Get)('debug/sync-info'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Debug: Get Sync Information' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "debugGetSyncInfo", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('webhook'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Handle Google Calendar Webhook' }),
    __param(0, (0, common_1.Headers)('x-goog-channel-id')),
    __param(1, (0, common_1.Headers)('x-goog-resource-id')),
    __param(2, (0, common_1.Headers)('x-goog-resource-state')),
    __param(3, (0, common_1.Headers)('x-goog-channel-token')),
    __param(4, (0, common_1.Headers)('x-goog-channel-expiration')),
    __param(5, (0, common_1.Headers)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], GoogleCalendarController.prototype, "handleWebhook", null);
exports.GoogleCalendarController = GoogleCalendarController = GoogleCalendarController_1 = __decorate([
    (0, swagger_1.ApiTags)('Google Calendar'),
    (0, common_1.Controller)('google-calendar'),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(clinic_user_entity_1.ClinicUser)),
    __param(3, (0, typeorm_1.InjectRepository)(brand_entity_1.Brand)),
    __metadata("design:paramtypes", [google_calendar_service_1.GoogleCalendarService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        encryption_service_1.EncryptionService])
], GoogleCalendarController);
//# sourceMappingURL=google-calendar.controller.js.map
import {
	Injectable,
	Logger,
	BadRequestException,
	UnauthorizedException,
	Inject,
	forwardRef,
	NotFoundException,
	ConflictException
} from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { google, Auth, calendar_v3 } from 'googleapis';
import { v4 as uuidv4 } from 'uuid';
import * as moment from 'moment-timezone';
import { User } from '../users/entities/user.entity';
import { getAdminPortalUrl } from '../utils/common/get-login-url';
import { Brand } from '../brands/entities/brand.entity';
import { GoogleSyncStatus } from '../users/enums/google-sync-status.enum';
import { EncryptionService } from '../utils/encryption/encryption.service';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { PatientOwner } from '../patients/entities/patient-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { GoogleCalendarCacheService } from './google-calendar-cache.service';
import { Not, IsNull } from 'typeorm';
import { RedisService } from '../utils/redis/redis.service';
import { GoogleCalendarErrorHandler } from '../utils/google-calendar/google-calendar-error.handler';

export interface GoogleCalendarInfo {
	id: string;
	summary: string;
	description?: string;
	primary?: boolean;
	accessRole: string;
}

export interface ConnectionStatus {
	isConnected: boolean;
	calendarId?: string;
	calendarName?: string;
	syncStatus?: GoogleSyncStatus;
	lastSyncAt?: Date;
	errorMessage?: string;
}

export interface OAuthCallbackResult {
	userId: string;
	success: boolean;
	message: string;
}

@Injectable()
export class GoogleCalendarService {
	private readonly logger = new Logger(GoogleCalendarService.name);
	private readonly oauth2Client: Auth.OAuth2Client;

	// Add connection operation locks to prevent race conditions
	private readonly connectionLocks = new Map<string, Promise<any>>();

	private readonly withRetry: <T>(op: () => Promise<T>) => Promise<T>;

	private readonly redisPubClient;

	constructor(
		@InjectRepository(User)
		private readonly userRepository: Repository<User>,
		private readonly configService: ConfigService,
		private readonly encryptionService: EncryptionService,
		@Inject(forwardRef(() => SqsService))
		private readonly sqsService: SqsService,
		private readonly cacheService: GoogleCalendarCacheService,
		private readonly redisService: RedisService,
		@InjectRepository(Brand)
		private readonly brandRepository: Repository<Brand>,
		private readonly errorHandler: GoogleCalendarErrorHandler
	) {
		// Pull credentials from the unified google.calendar config namespace
		this.oauth2Client = new google.auth.OAuth2(
			this.configService.get<string>('google.calendar.clientId'),
			this.configService.get<string>('google.calendar.clientSecret'),
			this.configService.get<string>('google.calendar.redirectUri')
		);
		this.redisPubClient = this.redisService.getPubClient();
		this.withRetry = <T>(op: () => Promise<T>) =>
			this.errorHandler.executeWithRetry(op);
	}

	/**
	 * Generate Google OAuth authorization URL
	 */
	async getAuthUrl(userId: string): Promise<string> {
		// Clear any previous error messages when user retries connection
		await this.userRepository.update(userId, {
			googleSyncErrorMessage: undefined
		});

		const scopes = [
			'https://www.googleapis.com/auth/calendar.events',
			'https://www.googleapis.com/auth/calendar.readonly'
		];
		const authUrl = this.oauth2Client.generateAuthUrl({
			access_type: 'offline',
			scope: scopes,
			prompt: 'consent', // Force consent to get refresh token
			state: userId // Pass user ID for callback identification
		});

		this.logger.log(`Generated auth URL for user ${userId}, cleared previous errors`);
		return authUrl;
	}

	/**
	 * Handle OAuth callback and exchange code for tokens
	 */
	async handleOAuthCallback(
		code: string,
		state: string
	): Promise<OAuthCallbackResult> {
		if (!state) {
			throw new BadRequestException('Missing state parameter (user ID)');
		}

		const userId = state;

		try {
			// Exchange authorization code for tokens
			const { tokens } = await this.oauth2Client.getToken(code);

			if (!tokens.refresh_token) {
				throw new UnauthorizedException(
					'No refresh token received. User may need to revoke access and try again.'
				);
			}

			// Update user with encrypted refresh token
			const encryptedRefreshToken = this.encryptionService.encrypt(
				tokens.refresh_token
			);

			await this.userRepository.update(userId, {
				googleCalendarRefreshToken: encryptedRefreshToken,
				googleSyncStatus: GoogleSyncStatus.PENDING,
				googleSyncErrorMessage: undefined,
				lastGoogleSyncAt: new Date()
			});

			this.logger.log(
				`Successfully stored OAuth tokens for user ${userId}`
			);

			return {
				userId,
				success: true,
				message: 'OAuth callback handled successfully'
			};
		} catch (error: any) {
			this.logger.error(
				`OAuth callback failed for user ${userId}`,
				error.stack
			);

			// Update user with error status
			await this.userRepository.update(userId, {
				googleSyncStatus: GoogleSyncStatus.FAILED,
				googleSyncErrorMessage: error.message
			});

			throw error;
		}
	}

	/**
	 * Get list of user's Google calendars
	 */
	async getUserCalendars(userId: string): Promise<GoogleCalendarInfo[]> {
		const user = await this.getUserWithTokens(userId);
		const calendar = await this.getAuthenticatedCalendarClient(user);

		try {
			const response = await this.withRetry(() =>
				calendar.calendarList.list({
					minAccessRole: 'writer' // Only calendars user can write to
				})
			);

			const calendars: GoogleCalendarInfo[] = (
				response.data.items || []
			).map(item => ({
				id: item.id!,
				summary: item.summary!,
				description: item.description ?? undefined,
				primary: item.primary ?? undefined,
				accessRole: item.accessRole!
			}));

			this.logger.log(
				`Retrieved ${calendars.length} calendars for user ${userId}`
			);
			return calendars;
		} catch (error: any) {
			this.logger.error(
				`Failed to get calendars for user ${userId}`,
				error.stack
			);
			throw new Error(`Failed to retrieve calendars: ${error.message}`);
		}
	}

	/**
	 * Connect user to a specific Google calendar.
	 * Uses a Redis-based distributed lock so that only one instance
	 * can run the connect flow for a given user at a time.
	 */
	async connectCalendar(
		userId: string,
		calendarId: string
	): Promise<{ calendarName: string }> {
		const lockKey = `gcal-connect-lock:${userId}`;
		const lockAcquired = await this.redisService.setLock(
			lockKey,
			'locked',
			30
		); // 30-second TTL

		if (!lockAcquired) {
			this.logger.warn(
				`Connection operation already in progress for user ${userId}`
			);
			throw new ConflictException(
				'Google Calendar connection already in progress. Please retry shortly.'
			);
		}

		try {
			return await this._performConnection(userId, calendarId);
		} finally {
			await this.redisService.releaseLock(lockKey);
		}
	}

	/**
	 * Internal method to perform the actual connection logic
	 */
	private async _performConnection(
		userId: string,
		calendarId: string
	): Promise<{ calendarName: string }> {
		const user = await this.getUserWithTokens(userId);
		const calendar = await this.getAuthenticatedCalendarClient(user);

		try {
			// Verify calendar exists and user has access
			const calendarResponse = await this.withRetry(() =>
				calendar.calendars.get({
					calendarId
				})
			);

			const calendarName =
				calendarResponse.data.summary || 'Unknown Calendar';

			// Update user with selected calendar in a transaction
			await this.userRepository.manager.transaction(
				async transactionManager => {
					await transactionManager.update(User, userId, {
						googleCalendarId: calendarId,
						isGoogleSyncEnabled: true,
						googleSyncStatus: GoogleSyncStatus.SUCCESS,
						googleSyncErrorMessage: undefined,
						lastGoogleSyncAt: new Date()
					});
				}
			);

			// Initiate webhook for push notifications
			await this.watchCalendar(userId, calendarId);

			// Trigger an initial full sync to populate local cache
			try {
				await this.performIncrementalSync(userId);
			} catch (syncErr) {
				this.logger.warn(
					`Initial full sync failed for user ${userId}: ${syncErr instanceof Error ? syncErr.message : syncErr}`
				);
			}

			this.logger.log(
				`User ${userId} connected to calendar: ${calendarName} (${calendarId})`
			);

			return { calendarName };
		} catch (error: any) {
			this.logger.error(
				`Failed to connect calendar for user ${userId}`,
				error.stack
			);

			await this.userRepository.update(userId, {
				googleSyncStatus: GoogleSyncStatus.FAILED,
				googleSyncErrorMessage: error.message
			});

			throw new Error(`Failed to connect calendar: ${error.message}`);
		}
	}

	/**
	 * Get user's Google Calendar connection status
	 */
	async getConnectionStatus(userId: string): Promise<ConnectionStatus> {
		const user = await this.userRepository.findOne({
			where: { id: userId }
		});

		if (!user) {
			throw new Error('User not found');
		}

		const isConnected = !!(
			user.googleCalendarRefreshToken && user.isGoogleSyncEnabled
		);

		let calendarName: string | undefined;
		if (isConnected && user.googleCalendarId) {
			try {
				const calendar =
					await this.getAuthenticatedCalendarClient(user);
				const calendarResponse = await calendar.calendars.get({
					calendarId: user.googleCalendarId
				});
				calendarName = calendarResponse.data.summary || undefined;
			} catch (error: any) {
				this.logger.warn(
					`Failed to get calendar name for user ${userId}`,
					error.message
				);
			}
		}

		return {
			isConnected,
			calendarId: user.googleCalendarId || undefined,
			calendarName,
			syncStatus: user.googleSyncStatus || undefined,
			lastSyncAt: user.lastGoogleSyncAt || undefined,
			errorMessage: user.googleSyncErrorMessage || undefined
		};
	}

	/**
	 * Disconnect user from Google Calendar
	 */
	async disconnectCalendar(userId: string): Promise<void> {
		const user = await this.getUserWithTokens(userId);

		// Stop existing webhook channel before disconnecting
		if (user.googleWebhookId && user.googleWebhookResourceId) {
			await this.stopWatchChannel(user);
		}

		// Clear cached events
		await this.cacheService.clearUserEvents(userId);

		// Purge all Google Event IDs from user's appointments
		await this.purgeGoogleEventIdsForUser(user);

		try {
			// Revoke Google tokens if possible
			if (user.googleCalendarRefreshToken) {
				try {
					const refreshToken = this.encryptionService.decrypt(
						user.googleCalendarRefreshToken
					);
					this.oauth2Client.setCredentials({
						refresh_token: refreshToken
					});
					await this.oauth2Client.revokeCredentials();
				} catch (error: any) {
					this.logger.warn(
						`Failed to revoke Google tokens for user ${userId}`,
						error.message
					);
				}
			}

			// Clear all Google Calendar data
			await this.userRepository.update(userId, {
				googleCalendarRefreshToken: undefined,
				googleCalendarId: undefined,
				googleSyncToken: undefined,
				isGoogleSyncEnabled: false,
				googleSyncStatus: undefined,
				googleSyncErrorMessage: undefined,
				lastGoogleSyncAt: undefined,
				googleWebhookToken: undefined // also clear webhook token
			});

			this.logger.log(`Cleared Google Calendar data for user ${userId}`);
		} catch (error: any) {
			this.logger.error(
				`Failed to disconnect user ${userId} from Google Calendar`,
				error.stack
			);
			throw new Error(`Failed to disconnect: ${error.message}`);
		}
	}

	/**
	 * Get user with Google Calendar tokens
	 */
	private async getUserWithTokens(userId: string) {
		const user = await this.userRepository.findOne({
			where: { id: userId },
			select: [
				'id',
				'email',
				'firstName',
				'lastName',
				'isGoogleSyncEnabled',
				'googleCalendarRefreshToken',
				'googleCalendarId',
				'googleSyncStatus',
				'lastGoogleSyncAt',
				'googleWebhookToken' // newly added for validation
			]
		});

		if (!user) {
			throw new Error('User not found');
		}

		return user;
	}

	/**
	 * Get authenticated Google Calendar client for user
	 */
	private async getAuthenticatedCalendarClient(
		user: User
	): Promise<calendar_v3.Calendar> {
		if (!user.googleCalendarRefreshToken) {
			throw new UnauthorizedException(
				'User not connected to Google Calendar'
			);
		}

		try {
			const refreshToken = this.encryptionService.decrypt(
				user.googleCalendarRefreshToken
			);

			this.oauth2Client.setCredentials({
				refresh_token: refreshToken
			});

			// Refresh access token if needed
			const tokenResponse = await this.oauth2Client.getAccessToken();

			const calendarClient = google.calendar({
				version: 'v3',
				auth: this.oauth2Client
			});

			return calendarClient;
		} catch (error: any) {
			this.logger.error(
				`Failed to authenticate calendar client for user ${user.id}:`,
				{
					error: error.message,
					errorCode: error.code,
					userId: user.id
				}
			);
			throw new UnauthorizedException(
				`Authentication failed: ${error.message}`
			);
		}
	}

	/**
	 * Create a watch channel on a calendar to get push notifications
	 */
	async watchCalendar(userId: string, calendarId: string): Promise<void> {
		const lockKey = `watch-${userId}`;

		// Prevent concurrent watch operations for the same user
		if (this.connectionLocks.has(lockKey)) {
			this.logger.warn(
				`Watch operation already in progress for user ${userId}, skipping duplicate`
			);
			return;
		}

		const watchPromise = this._performWatchSetup(userId, calendarId);
		this.connectionLocks.set(lockKey, watchPromise);

		try {
			await watchPromise;
		} finally {
			this.connectionLocks.delete(lockKey);
		}
	}

	/**
	 * Internal method to perform watch channel setup
	 */
	private async _performWatchSetup(
		userId: string,
		calendarId: string
	): Promise<void> {
		const user = await this.getUserWithTokens(userId);
		const calendar = await this.getAuthenticatedCalendarClient(user);
		const webhookUrl = this.configService.get<string>(
			'google.calendar.webhookUrl'
		);

		if (!webhookUrl) {
			this.logger.warn(
				`Google webhook URL is not configured. Skipping watch setup for user ${userId}.`
			);
			return;
		}

		try {
			// If a channel already exists, stop it first and wait for completion
			if (user.googleWebhookId && user.googleWebhookResourceId) {
				this.logger.log(
					`Stopping existing watch channel ${user.googleWebhookId} for user ${userId}`
				);
				await this.stopWatchChannel(user);
				// Refetch user to get updated webhook fields
				const updatedUser = await this.getUserWithTokens(userId);
				if (updatedUser.googleWebhookId) {
					this.logger.warn(
						`Previous channel not fully cleaned up for user ${userId}, forcing cleanup`
					);
					await this.userRepository.update(userId, {
						googleWebhookId: undefined,
						googleWebhookResourceId: undefined
					});
				}
			}

			const channelId = uuidv4();
			const webhookToken = uuidv4(); // Generate a secure token

			this.logger.log(
				`Creating new watch channel ${channelId} for user ${userId} on calendar ${calendarId}`
			);

			const ttlSeconds = 604800; // 7 days
			const response = await this.withRetry(() =>
				calendar.events.watch({
					calendarId,
					requestBody: {
						id: channelId,
						type: 'web_hook',
						address: webhookUrl,
						params: {
							ttl: ttlSeconds.toString()
						},
						token: webhookToken // Include the secure token
					}
				})
			);

			// Parse expiration (ms epoch string) returned by Google
			let expiresAt: Date | undefined;
			if (response.data.expiration) {
				const expNum = Number(response.data.expiration);
				if (!isNaN(expNum)) {
					expiresAt = new Date(expNum);
				}
			}

			// Update user with new webhook info in a transaction
			await this.userRepository.manager.transaction(
				async transactionManager => {
					await transactionManager.update(User, userId, {
						googleWebhookId: response.data.id || undefined,
						googleWebhookResourceId:
							response.data.resourceId || undefined,
						googleWebhookExpiresAt: expiresAt,
						// Store the secure token encrypted to reduce exposure risk
						googleWebhookToken:
							this.encryptionService.encrypt(webhookToken)
					});
				}
			);

			this.logger.log(
				`Successfully created watch channel ${response.data.id} for user ${userId}`
			);
		} catch (error: any) {
			this.logger.error(
				`Failed to create watch channel for user ${userId}`,
				error.stack
			);
			await this.userRepository.update(userId, {
				googleSyncStatus: GoogleSyncStatus.FAILED,
				googleSyncErrorMessage: `Failed to create webhook: ${error.message}`
			});
			throw error;
		}
	}

	/**
	 * Stop a watch channel
	 */
	async stopWatchChannel(user: User): Promise<void> {
		if (!user.googleWebhookId || !user.googleWebhookResourceId) {
			this.logger.warn(`User ${user.id} has no webhook to stop.`);
			return;
		}

		const calendar = await this.getAuthenticatedCalendarClient(user);

		try {
			await this.withRetry(() =>
				calendar.channels.stop({
					requestBody: {
						id: user.googleWebhookId,
						resourceId: user.googleWebhookResourceId
					}
				})
			);

			this.logger.log(
				`Successfully stopped watch channel ${user.googleWebhookId} for user ${user.id}`
			);

			// Clear the webhook IDs from the user entity after successful stop
			await this.userRepository.update(user.id, {
				googleWebhookId: undefined,
				googleWebhookResourceId: undefined
			});
		} catch (error: any) {
			// A 404 error means the channel doesn't exist anymore, which is fine.
			if (error.code === 404) {
				this.logger.warn(
					`Watch channel ${user.googleWebhookId} not found on Google's side for user ${user.id}. Clearing from DB.`
				);
				// Clear the orphaned webhook IDs from the user entity
				await this.userRepository.update(user.id, {
					googleWebhookId: undefined,
					googleWebhookResourceId: undefined
				});
			} else {
				this.logger.error(
					`Failed to stop watch channel for user ${user.id}`,
					error.stack
				);
				// We still clear the IDs from our side to avoid orphaned webhooks
			}
		}
	}

	/**
	 * Process an incoming webhook notification from Google
	 * @param channelId - The ID of the watch channel.
	 * @param resourceId - The opaque ID of the resource being watched.
	 * @param resourceState - The state of the resource (e.g., "sync", "exists", "not_exists").
	 */
	async processWebhookNotification(
		channelId: string,
		resourceId: string,
		resourceState: string
	) {
		if (resourceState === 'sync') {
			// This is a sync request, not a real notification.
			// We may need to re-sync everything if we get this.
			const user = await this.userRepository.findOne({
				where: { googleWebhookId: channelId }
			});
			if (user) {
				// Optional: Trigger a full re-sync here
			}
			return;
		}

		if (resourceState === 'not_exists') {
			return;
		}

		// Find user by channel ID
		const user = await this.userRepository.findOne({
			where: {
				googleWebhookId: channelId,
				googleWebhookResourceId: resourceId
			}
		});

		if (!user) {
			// Also try finding by channelId only
			const userByChannelId = await this.userRepository.findOne({
				where: { googleWebhookId: channelId }
			});
			if (userByChannelId) {
				this.logger.warn(
					`Webhook found user by channelId only, but resourceId mismatch. Expected: ${resourceId}, Found: ${userByChannelId.googleWebhookResourceId}`
				);
			}
			return;
		}

		try {
			// Enqueue a job to perform incremental sync for this user.
			await this.sqsService.sendMessage({
				queueKey: 'NidanaGoogleCalendarSync',
				messageBody: {
					data: {
						type: 'WEBHOOK_NOTIFICATION',
						userId: user.id,
						timestamp: new Date().toISOString(),
						webhookData: {
							channelId,
							resourceId,
							resourceState
						}
					}
				},
				deduplicationId: `webhook-${channelId}-${resourceId}-${Date.now()}`
			});
		} catch (error) {
			this.logger.error(
				`Failed to queue SQS message for user ${user.id}:`,
				error
			);
		}
	}

	/**
	 * Perform incremental sync with Google Calendar for a user
	 */
	async performIncrementalSync(userId: string) {
		const user = await this.getUserWithTokens(userId);
		const calendar = await this.getAuthenticatedCalendarClient(user);
		let syncToken = user.googleSyncToken;

		if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
			this.logger.warn(
				`Sync is not enabled for user ${userId}. isGoogleSyncEnabled: ${user.isGoogleSyncEnabled}, googleCalendarId: ${user.googleCalendarId}`
			);
			return;
		}

		try {
			const fullSync = !syncToken;

			if (fullSync) {
				// Prepare to fetch one year historical events
				syncToken = undefined;
			}

			let nextPageToken: string | undefined;
			let response: any;
			const eventsToUpsert: any[] = [];
			do {
				response = await this.withRetry(() =>
					calendar.events.list({
						calendarId: user.googleCalendarId,
						pageToken: nextPageToken,
						showDeleted: true,
						singleEvents: true, // expand recurring events into single instances
						...(fullSync
							? {
									timeMin: moment()
										.subtract(1, 'year')
										.toISOString()
								}
							: { syncToken: syncToken! })
					})
				);

				const events = response.data.items;

				this.logger.log(
					`Fetched ${events?.length || 0} events from Google Calendar for user ${userId} (page token: ${nextPageToken || 'none'})`
				);

				if (events && events.length > 0) {
					for (const event of events) {
						// Log each event for debugging
						this.logger.debug(
							`Processing event: ${event.id} - ${event.summary} (${event.start?.dateTime || event.start?.date}) - Status: ${event.status}`
						);

						// Removed skip for recurring events to ensure they are synced
						if (event.status === 'cancelled') {
							// Handle deleted event
							// Remove from cache if exists
							await this.cacheService.upsertEvent(userId, {
								id: event.id,
								calendarId: user.googleCalendarId!,
								start: {
									dateTime: event.start?.dateTime,
									date: event.start?.date
								},
								end: {
									dateTime: event.end?.dateTime,
									date: event.end?.date
								},
								status: 'cancelled'
							});
						} else {
							// collect to upsert later in batch
							eventsToUpsert.push(event);
						}
					}
				}

				nextPageToken = response.data.nextPageToken;
				// After the first page, subsequent requests must not send syncToken
				if (nextPageToken) {
					syncToken = undefined;
				}
			} while (nextPageToken);

			// save/upsert batch into cache
			if (eventsToUpsert.length > 0) {
				this.logger.log(
					`Saving ${eventsToUpsert.length} events to cache for user ${userId}`
				);
				await this.cacheService.saveEvents(userId, eventsToUpsert);
				this.logger.log(
					`Successfully saved ${eventsToUpsert.length} events to cache for user ${userId}`
				);
			} else {
				this.logger.log(`No events to save for user ${userId}`);
			}

			// Save the new sync token for the next sync
			const newSyncToken = response.data.nextSyncToken;
			if (newSyncToken) {
				await this.userRepository.update(userId, {
					googleSyncToken: newSyncToken,
					lastGoogleSyncAt: new Date(),
					googleSyncStatus: GoogleSyncStatus.SUCCESS,
					googleSyncErrorMessage: undefined
				});
				this.logger.log(
					`Incremental sync completed for user ${userId}. New sync token stored.`
				);
				// Notify front-end via Redis that calendar data changed
				try {
					await this.redisPubClient.publish(
						'calendar-updates',
						JSON.stringify({ userId })
					);
				} catch (pubErr) {
					this.logger.warn(
						`Failed to publish calendar update for user ${userId}: ${pubErr instanceof Error ? pubErr.message : pubErr}`
					);
				}
			}
		} catch (error: any) {
			if (error.code === 410) {
				// A 410 error indicates the sync token is invalid.
				this.logger.warn(
					`Sync token for user ${userId} is invalid. Clearing token to trigger full sync.`
				);
				await this.userRepository.update(userId, {
					googleSyncToken: undefined,
					googleSyncStatus: GoogleSyncStatus.FAILED,
					googleSyncErrorMessage:
						'Sync token became invalid; a full sync is required.'
				});
				// Optional: Trigger a full sync here
			} else {
				this.logger.error(
					`Incremental sync failed for user ${userId}`,
					error.stack
				);
				await this.userRepository.update(userId, {
					googleSyncStatus: GoogleSyncStatus.FAILED,
					googleSyncErrorMessage: `Sync failed: ${error.message}`
				});
			}
		}
	}

	/**
	 * Creates an event on Google Calendar.
	 */
	async createEvent(
		userId: string,
		appointment: AppointmentEntity
	): Promise<string | null> {
		try {
			const user = await this.getUserWithTokens(userId);

			if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
				return null;
			}

			const calendar = await this.getAuthenticatedCalendarClient(user);
			const eventDetails = await this.buildEventDetails(appointment);

			const response = await this.withRetry(() =>
				calendar.events.insert({
					calendarId: user.googleCalendarId,
					requestBody: eventDetails
				})
			);

			if (response.data.id) {
				return response.data.id;
			} else {
				return null;
			}
		} catch (error: any) {
			this.logger.error(
				`Failed to create Google Calendar event for appointment ${appointment.id}:`,
				{
					error: error.message,
					errorCode: error.code,
					errorStatus: error.status,
					userId,
					appointmentId: appointment.id
				}
			);

			// Check for specific error types
			if (error.code === 401 || error.status === 401) {
				this.logger.error(
					`Authentication error - user may need to reconnect Google Calendar`
				);
			} else if (error.code === 403 || error.status === 403) {
				this.logger.error(
					`Permission error - check calendar access permissions`
				);
			} else if (error.code === 429 || error.status === 429) {
				this.logger.error(`Rate limit exceeded - should retry later`);
			}

			return null;
		}
	}

	/**
	 * Updates an event on Google Calendar.
	 */
	async updateEvent(
		userId: string,
		appointment: AppointmentEntity
	): Promise<string | null> {
		if (!appointment.googleEventId) {
			return this.createEvent(userId, appointment);
		}

		const user = await this.getUserWithTokens(userId);
		if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
			return null;
		}
		const calendar = await this.getAuthenticatedCalendarClient(user);
		const eventDetails = await this.buildEventDetails(appointment);

		try {
			const response = await this.withRetry(() =>
				calendar.events.update({
					calendarId: user.googleCalendarId,
					eventId: appointment.googleEventId,
					requestBody: eventDetails
				})
			);
			this.logger.log(
				`Updated Google Calendar event ${response.data.id} for appointment ${appointment.id}`
			);
			return response.data.id || null;
		} catch (error) {
			this.logger.error(
				`Failed to update Google Calendar event for appointment ${appointment.id}`,
				error
			);
			// Optional: Queue for retry
			return null;
		}
	}

	/**
	 * Deletes an event from Google Calendar.
	 */
	async deleteEvent(userId: string, googleEventId: string): Promise<void> {
		const user = await this.getUserWithTokens(userId);
		if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
			return;
		}
		const calendar = await this.getAuthenticatedCalendarClient(user);

		try {
			await this.withRetry(() =>
				calendar.events.delete({
					calendarId: user.googleCalendarId,
					eventId: googleEventId
				})
			);
			this.logger.log(`Deleted Google Calendar event ${googleEventId}`);
		} catch (error) {
			this.logger.error(
				`Failed to delete Google Calendar event ${googleEventId}`,
				error
			);
			// Optional: Queue for retry
		}
	}

	/**
	 * Get events from Google Calendar for a specific date range
	 */
	async getEventsForDate(
		userId: string,
		_calendarId: string,
		startDate: Date,
		endDate: Date
	): Promise<any[]> {
		// Return cached events within range for the user.
		try {
			const cached = await this.cacheService.getEventsRange(
				userId,
				startDate,
				endDate
			);
			this.logger.log(
				`Retrieved ${cached.length} cached events for user ${userId} between ${startDate.toISOString()} and ${endDate.toISOString()}`
			);
			return cached.map(
				c =>
					c.raw ?? {
						id: c.eventId,
						summary: c.summary,
						description: c.description,
						start: { dateTime: c.startTime.toISOString() },
						end: { dateTime: c.endTime.toISOString() },
						status: c.status
					}
			);
		} catch (error) {
			this.logger.error(
				`Failed to read cached events for user ${userId}`,
				error
			);
			return [];
		}
	}

	/**
	 * Debug method to get detailed sync information
	 */
	async getDebugSyncInfo(userId: string): Promise<any> {
		try {
			const user = await this.userRepository.findOne({
				where: { id: userId }
			});

			if (!user) {
				return { error: 'User not found' };
			}

			// Get cached events count
			const today = new Date();
			today.setHours(0, 0, 0, 0);
			const tomorrow = new Date(today);
			tomorrow.setDate(tomorrow.getDate() + 1);

			const cachedEvents = await this.cacheService.getEventsRange(
				userId,
				today,
				tomorrow
			);

			return {
				user: {
					id: user.id,
					email: user.email,
					isGoogleSyncEnabled: user.isGoogleSyncEnabled,
					googleCalendarId: user.googleCalendarId,
					googleSyncStatus: user.googleSyncStatus,
					googleSyncErrorMessage: user.googleSyncErrorMessage,
					lastGoogleSyncAt: user.lastGoogleSyncAt,
					googleWebhookId: user.googleWebhookId,
					googleWebhookExpiresAt: user.googleWebhookExpiresAt
				},
				cachedEventsToday: {
					count: cachedEvents.length,
					events: cachedEvents.map(e => ({
						eventId: e.eventId,
						summary: e.summary,
						startTime: e.startTime,
						endTime: e.endTime,
						status: e.status
					}))
				}
			};
		} catch (error: any) {
			this.logger.error(`Failed to get debug sync info for user ${userId}`, error);
			return { error: error.message };
		}
	}

	private async resolveBrandSlug(
		appointment: AppointmentEntity
	): Promise<string | undefined> {
		// Attempt 1: direct relation if loaded
		let slug = appointment.clinic?.brand?.slug;
		if (slug) {
			return slug;
		}
		// Attempt 2: fetch via brandId (from appointment or clinic)
		const brandId = appointment.brandId || appointment.clinic?.brandId;
		if (!brandId) return undefined;
		try {
			const brand = await this.brandRepository.findOne({
				where: { id: brandId }
			});
			return brand?.slug;
		} catch (err) {
			this.logger.warn(
				`Failed to resolve brand slug for brandId ${brandId}: ${err instanceof Error ? err.message : err}`
			);
			return undefined;
		}
	}

	private async buildEventDetails(
		appointment: AppointmentEntity
	): Promise<calendar_v3.Schema$Event> {
		// Derive patient and clinic names if available, fallback to IDs otherwise
		const patientName =
			appointment.patient?.patientName || appointment.patientId;
		const clinicName = appointment.clinic?.name || appointment.clinicId;

		// Extract primary (or first) owner details if they are loaded
		let ownerName: string | undefined;
		let ownerPhone: string | undefined;
		const owners = appointment.patient?.patientOwners;
		if (owners && owners.length > 0) {
			// Prefer explicitly marked primary owner, otherwise take the first
			const primaryOwner =
				owners.find(o => (o as any).isPrimary) ?? owners[0];
			const ownerBrand = (primaryOwner as any)?.ownerBrand;
			if (ownerBrand) {
				ownerName =
					`${ownerBrand.firstName ?? ''} ${ownerBrand.lastName ?? ''}`.trim();
				ownerPhone = ownerBrand.globalOwner?.phoneNumber;
			}
		}

		// Determine the correct brand slug (relation may or may not be loaded)
		const brandSlug = await this.resolveBrandSlug(appointment);
		const baseAdminUrl = getAdminPortalUrl(brandSlug);
		const nidanaLink = `${baseAdminUrl}/patients/${appointment.patientId}/details`;

		// Build event description with optional owner details
		const descriptionLines: string[] = [
			'Appointment Details:',
			`- Reason: ${appointment.reason || ''}`,
			`- Patient: ${patientName}`
		];
		if (ownerName) {
			descriptionLines.push(
				`- Owner: ${ownerName}${ownerPhone ? ` (${ownerPhone})` : ''}`
			);
		}
		descriptionLines.push(`- Clinic: ${clinicName}`);
		descriptionLines.push(`- View in Nidana: ${nidanaLink}`);

		const description = descriptionLines.join('\n');

		// Handle both Date objects and ISO strings for start/end times
		const startDateTime =
			appointment.startTime instanceof Date
				? appointment.startTime.toISOString()
				: appointment.startTime;
		const endDateTime =
			appointment.endTime instanceof Date
				? appointment.endTime.toISOString()
				: appointment.endTime;

		// Determine clinic timezone
		const clinicTz = appointment.clinic?.timezone || 'UTC';

		// Provide a meaningful title for the event (include patient name)
		const baseTitle =
			appointment.type || appointment.reason?.trim() || 'Appointment';

		const eventTitle = `${patientName} - ${baseTitle}`;

		// const eventTitle = clinicName
		// 	? `${clinicName}: ${patientName} - ${baseTitle}`
		// 	: `${patientName} - ${baseTitle}`;

		return {
			summary: eventTitle,
			description,
			start: {
				dateTime: startDateTime,
				timeZone: clinicTz
			},
			end: {
				dateTime: endDateTime,
				timeZone: clinicTz
			},
			// attendees: [{ email: '<EMAIL>' }], // Future enhancement
			extendedProperties: {
				private: {
					nidanaAppointmentId: appointment.id
				}
			}
		};
	}

	/**
	 * Purges all Google Event IDs from appointments associated with a user.
	 */
	private async purgeGoogleEventIdsForUser(user: User): Promise<void> {
		if (!user.mobileNumber) {
			this.logger.warn(
				`User ${user.id} has no mobile number, cannot purge event IDs.`
			);
			return;
		}

		try {
			const appointmentRepository =
				this.userRepository.manager.getRepository(AppointmentEntity);

			// Find all appointment IDs that need to be purged
			const appointmentsToUpdate = await appointmentRepository
				.createQueryBuilder('appointment')
				.innerJoin(
					PatientOwner,
					'po',
					'po.patientId = appointment.patientId'
				)
				.innerJoin(OwnerBrand, 'ob', 'ob.id = po.ownerId')
				.innerJoin(GlobalOwner, 'go', 'go.id = ob.globalOwnerId')
				.where('go.phoneNumber = :phoneNumber', {
					phoneNumber: user.mobileNumber
				})
				.andWhere('appointment.googleEventId IS NOT NULL')
				.select('appointment.id')
				.getMany();

			if (appointmentsToUpdate.length > 0) {
				const appointmentIds = appointmentsToUpdate.map(a => a.id);
				await appointmentRepository.update(appointmentIds, {
					googleEventId: undefined
				});
				this.logger.log(
					`Purged Google Event IDs from ${appointmentIds.length} appointments for user ${user.id}.`
				);
			}
		} catch (error: any) {
			this.logger.error(
				`Failed to purge Google Event IDs for user ${user.id}`,
				error.stack
			);
			// Do not re-throw, as failing to purge event IDs should not block disconnection
		}
	}

	/**
	 * Cleanup orphaned watch channels (can be called periodically)
	 */
	async cleanupOrphanedChannels(): Promise<void> {
		this.logger.log(
			'Starting cleanup of orphaned Google Calendar watch channels'
		);

		try {
			// Find all users with webhook IDs but potentially orphaned channels
			const usersWithWebhooks = await this.userRepository.find({
				where: {
					googleWebhookId: Not(IsNull()),
					isGoogleSyncEnabled: true
				}
			});

			this.logger.log(
				`Found ${usersWithWebhooks.length} users with active webhooks to verify`
			);

			for (const user of usersWithWebhooks) {
				try {
					// Try to verify if the channel still exists by attempting to stop it
					// If it fails with "not found", we know it's orphaned
					if (user.googleWebhookId && user.googleWebhookResourceId) {
						const calendar =
							await this.getAuthenticatedCalendarClient(user);

						// Attempt to stop the channel - this will fail if it doesn't exist
						await calendar.channels.stop({
							requestBody: {
								id: user.googleWebhookId,
								resourceId: user.googleWebhookResourceId
							}
						});

						this.logger.log(
							`Channel ${user.googleWebhookId} for user ${user.id} is still active`
						);
					}
				} catch (error: any) {
					// If we get a "not found" error, the channel is orphaned
					if (
						error.message?.includes('not found') ||
						error.status === 404
					) {
						this.logger.warn(
							`Cleaning up orphaned channel ${user.googleWebhookId} for user ${user.id}`
						);

						await this.userRepository.update(user.id, {
							googleWebhookId: undefined,
							googleWebhookResourceId: undefined
						});
					} else {
						this.logger.warn(
							`Could not verify channel ${user.googleWebhookId} for user ${user.id}: ${error.message}`
						);
					}
				}
			}

			this.logger.log(
				'Completed cleanup of orphaned Google Calendar watch channels'
			);
		} catch (error: any) {
			this.logger.error(
				'Failed to cleanup orphaned channels',
				error.stack
			);
		}
	}

	/**
	 * Daily cron job to renew channels expiring within 24 hours.
	 */
	@Cron('0 3 * * *') // 03:00 AM daily
	async renewExpiringChannels() {
		this.logger.log(
			'Starting renewal check for expiring Google Calendar channels'
		);
		const now = new Date();
		const cutoff = new Date(now.getTime() + 24 * 60 * 60 * 1000);
		try {
			const expiringUsers = await this.userRepository.find({
				where: {
					isGoogleSyncEnabled: true,
					googleWebhookExpiresAt: Not(IsNull())
				}
			});

			for (const user of expiringUsers) {
				if (
					user.googleWebhookExpiresAt &&
					user.googleWebhookExpiresAt <= cutoff
				) {
					this.logger.log(`Renewing channel for user ${user.id}`);
					if (user.googleCalendarId) {
						try {
							await this.watchCalendar(
								user.id,
								user.googleCalendarId
							);
						} catch (err) {
							this.logger.error(
								`Failed to renew channel for user ${user.id}`,
								err
							);
						}
					}
				}
			}
		} catch (err) {
			this.logger.error('Error during channel renewal job', err);
		}
	}
}

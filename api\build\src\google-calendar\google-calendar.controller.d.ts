import { Response } from 'express';
import { GoogleCalendarService } from './google-calendar.service';
import { User } from '../users/entities/user.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { Brand } from '../brands/entities/brand.entity';
import { AuthenticatedUser } from '../auth/interfaces/authenticated-user.interface';
import { Repository } from 'typeorm';
import { EncryptionService } from '../utils/encryption/encryption.service';
export declare class GoogleCalendarController {
    private readonly googleCalendarService;
    private readonly userRepository;
    private readonly clinicUserRepository;
    private readonly brandRepository;
    private readonly encryptionService;
    private readonly logger;
    constructor(googleCalendarService: GoogleCalendarService, userRepository: Repository<User>, clinicUserRepository: Repository<ClinicUser>, brandRepository: Repository<Brand>, encryptionService: EncryptionService);
    getAuthUrl(req: {
        user: AuthenticatedUser;
    }): Promise<{
        url: string;
    }>;
    handleOAuthCallback(code: string, state: string, res: Response): Promise<void | Response<any, Record<string, any>>>;
    listCalendars(req: {
        user: AuthenticatedUser;
    }): Promise<import("./google-calendar.service").GoogleCalendarInfo[]>;
    connectCalendar(req: {
        user: AuthenticatedUser;
    }, calendarId: string): Promise<{
        calendarName: string;
    }>;
    getConnectionStatus(req: {
        user: AuthenticatedUser;
    }): Promise<import("./google-calendar.service").ConnectionStatus>;
    disconnectCalendar(req: {
        user: AuthenticatedUser;
    }): Promise<void>;
    debugManualSync(req: {
        user: AuthenticatedUser;
    }): Promise<{
        success: boolean;
        message: string;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        message?: undefined;
    }>;
    debugGetCachedEvents(req: {
        user: AuthenticatedUser;
    }, startDate?: string, endDate?: string): Promise<{
        success: boolean;
        events: any[];
        count: number;
        dateRange: {
            start: Date;
            end: Date;
        };
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        events?: undefined;
        count?: undefined;
        dateRange?: undefined;
    }>;
    debugGetSyncInfo(req: {
        user: AuthenticatedUser;
    }): Promise<any>;
    handleWebhook(channelId: string, resourceId: string, resourceState: string, channelToken: string, channelExpiration: string, allHeaders: any): Promise<{
        success: boolean;
        timestamp: string;
    }>;
}

import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { User } from '../users/entities/user.entity';
import { Brand } from '../brands/entities/brand.entity';
import { GoogleSyncStatus } from '../users/enums/google-sync-status.enum';
import { EncryptionService } from '../utils/encryption/encryption.service';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { SqsService } from '../utils/aws/sqs/sqs.service';
import { GoogleCalendarCacheService } from './google-calendar-cache.service';
import { RedisService } from '../utils/redis/redis.service';
import { GoogleCalendarErrorHandler } from '../utils/google-calendar/google-calendar-error.handler';
export interface GoogleCalendarInfo {
    id: string;
    summary: string;
    description?: string;
    primary?: boolean;
    accessRole: string;
}
export interface ConnectionStatus {
    isConnected: boolean;
    calendarId?: string;
    calendarName?: string;
    syncStatus?: GoogleSyncStatus;
    lastSyncAt?: Date;
    errorMessage?: string;
}
export interface OAuthCallbackResult {
    userId: string;
    success: boolean;
    message: string;
}
export declare class GoogleCalendarService {
    private readonly userRepository;
    private readonly configService;
    private readonly encryptionService;
    private readonly sqsService;
    private readonly cacheService;
    private readonly redisService;
    private readonly brandRepository;
    private readonly errorHandler;
    private readonly logger;
    private readonly oauth2Client;
    private readonly connectionLocks;
    private readonly withRetry;
    private readonly redisPubClient;
    constructor(userRepository: Repository<User>, configService: ConfigService, encryptionService: EncryptionService, sqsService: SqsService, cacheService: GoogleCalendarCacheService, redisService: RedisService, brandRepository: Repository<Brand>, errorHandler: GoogleCalendarErrorHandler);
    /**
     * Generate Google OAuth authorization URL
     */
    getAuthUrl(userId: string): Promise<string>;
    /**
     * Handle OAuth callback and exchange code for tokens
     */
    handleOAuthCallback(code: string, state: string): Promise<OAuthCallbackResult>;
    /**
     * Get list of user's Google calendars
     */
    getUserCalendars(userId: string): Promise<GoogleCalendarInfo[]>;
    /**
     * Connect user to a specific Google calendar.
     * Uses a Redis-based distributed lock so that only one instance
     * can run the connect flow for a given user at a time.
     */
    connectCalendar(userId: string, calendarId: string): Promise<{
        calendarName: string;
    }>;
    /**
     * Internal method to perform the actual connection logic
     */
    private _performConnection;
    /**
     * Get user's Google Calendar connection status
     */
    getConnectionStatus(userId: string): Promise<ConnectionStatus>;
    /**
     * Disconnect user from Google Calendar
     */
    disconnectCalendar(userId: string): Promise<void>;
    /**
     * Get user with Google Calendar tokens
     */
    private getUserWithTokens;
    /**
     * Get authenticated Google Calendar client for user
     */
    private getAuthenticatedCalendarClient;
    /**
     * Create a watch channel on a calendar to get push notifications
     */
    watchCalendar(userId: string, calendarId: string): Promise<void>;
    /**
     * Internal method to perform watch channel setup
     */
    private _performWatchSetup;
    /**
     * Stop a watch channel
     */
    stopWatchChannel(user: User): Promise<void>;
    /**
     * Process an incoming webhook notification from Google
     * @param channelId - The ID of the watch channel.
     * @param resourceId - The opaque ID of the resource being watched.
     * @param resourceState - The state of the resource (e.g., "sync", "exists", "not_exists").
     */
    processWebhookNotification(channelId: string, resourceId: string, resourceState: string): Promise<void>;
    /**
     * Perform incremental sync with Google Calendar for a user
     */
    performIncrementalSync(userId: string): Promise<void>;
    /**
     * Creates an event on Google Calendar.
     */
    createEvent(userId: string, appointment: AppointmentEntity): Promise<string | null>;
    /**
     * Updates an event on Google Calendar.
     */
    updateEvent(userId: string, appointment: AppointmentEntity): Promise<string | null>;
    /**
     * Deletes an event from Google Calendar.
     */
    deleteEvent(userId: string, googleEventId: string): Promise<void>;
    /**
     * Get events from Google Calendar for a specific date range
     */
    getEventsForDate(userId: string, _calendarId: string, startDate: Date, endDate: Date): Promise<any[]>;
    /**
     * Debug method to get detailed sync information
     */
    getDebugSyncInfo(userId: string): Promise<any>;
    private resolveBrandSlug;
    private buildEventDetails;
    /**
     * Purges all Google Event IDs from appointments associated with a user.
     */
    private purgeGoogleEventIdsForUser;
    /**
     * Cleanup orphaned watch channels (can be called periodically)
     */
    cleanupOrphanedChannels(): Promise<void>;
    /**
     * Daily cron job to renew channels expiring within 24 hours.
     */
    renewExpiringChannels(): Promise<void>;
}

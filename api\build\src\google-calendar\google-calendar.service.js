"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var GoogleCalendarService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleCalendarService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const googleapis_1 = require("googleapis");
const uuid_1 = require("uuid");
const moment = require("moment-timezone");
const user_entity_1 = require("../users/entities/user.entity");
const get_login_url_1 = require("../utils/common/get-login-url");
const brand_entity_1 = require("../brands/entities/brand.entity");
const google_sync_status_enum_1 = require("../users/enums/google-sync-status.enum");
const encryption_service_1 = require("../utils/encryption/encryption.service");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const sqs_service_1 = require("../utils/aws/sqs/sqs.service");
const google_calendar_cache_service_1 = require("./google-calendar-cache.service");
const typeorm_3 = require("typeorm");
const redis_service_1 = require("../utils/redis/redis.service");
const google_calendar_error_handler_1 = require("../utils/google-calendar/google-calendar-error.handler");
let GoogleCalendarService = GoogleCalendarService_1 = class GoogleCalendarService {
    constructor(userRepository, configService, encryptionService, sqsService, cacheService, redisService, brandRepository, errorHandler) {
        this.userRepository = userRepository;
        this.configService = configService;
        this.encryptionService = encryptionService;
        this.sqsService = sqsService;
        this.cacheService = cacheService;
        this.redisService = redisService;
        this.brandRepository = brandRepository;
        this.errorHandler = errorHandler;
        this.logger = new common_1.Logger(GoogleCalendarService_1.name);
        // Add connection operation locks to prevent race conditions
        this.connectionLocks = new Map();
        // Pull credentials from the unified google.calendar config namespace
        this.oauth2Client = new googleapis_1.google.auth.OAuth2(this.configService.get('google.calendar.clientId'), this.configService.get('google.calendar.clientSecret'), this.configService.get('google.calendar.redirectUri'));
        this.redisPubClient = this.redisService.getPubClient();
        this.withRetry = (op) => this.errorHandler.executeWithRetry(op);
    }
    /**
     * Generate Google OAuth authorization URL
     */
    async getAuthUrl(userId) {
        // Clear any previous error messages when user retries connection
        await this.userRepository.update(userId, {
            googleSyncErrorMessage: undefined
        });
        const scopes = [
            'https://www.googleapis.com/auth/calendar.events',
            'https://www.googleapis.com/auth/calendar.readonly'
        ];
        const authUrl = this.oauth2Client.generateAuthUrl({
            access_type: 'offline',
            scope: scopes,
            prompt: 'consent', // Force consent to get refresh token
            state: userId // Pass user ID for callback identification
        });
        this.logger.log(`Generated auth URL for user ${userId}, cleared previous errors`);
        return authUrl;
    }
    /**
     * Handle OAuth callback and exchange code for tokens
     */
    async handleOAuthCallback(code, state) {
        if (!state) {
            throw new common_1.BadRequestException('Missing state parameter (user ID)');
        }
        const userId = state;
        try {
            // Exchange authorization code for tokens
            const { tokens } = await this.oauth2Client.getToken(code);
            if (!tokens.refresh_token) {
                throw new common_1.UnauthorizedException('No refresh token received. User may need to revoke access and try again.');
            }
            // Update user with encrypted refresh token
            const encryptedRefreshToken = this.encryptionService.encrypt(tokens.refresh_token);
            await this.userRepository.update(userId, {
                googleCalendarRefreshToken: encryptedRefreshToken,
                googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.PENDING,
                googleSyncErrorMessage: undefined,
                lastGoogleSyncAt: new Date()
            });
            this.logger.log(`Successfully stored OAuth tokens for user ${userId}`);
            return {
                userId,
                success: true,
                message: 'OAuth callback handled successfully'
            };
        }
        catch (error) {
            this.logger.error(`OAuth callback failed for user ${userId}`, error.stack);
            // Update user with error status
            await this.userRepository.update(userId, {
                googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.FAILED,
                googleSyncErrorMessage: error.message
            });
            throw error;
        }
    }
    /**
     * Get list of user's Google calendars
     */
    async getUserCalendars(userId) {
        const user = await this.getUserWithTokens(userId);
        const calendar = await this.getAuthenticatedCalendarClient(user);
        try {
            const response = await this.withRetry(() => calendar.calendarList.list({
                minAccessRole: 'writer' // Only calendars user can write to
            }));
            const calendars = (response.data.items || []).map(item => {
                var _a, _b;
                return ({
                    id: item.id,
                    summary: item.summary,
                    description: (_a = item.description) !== null && _a !== void 0 ? _a : undefined,
                    primary: (_b = item.primary) !== null && _b !== void 0 ? _b : undefined,
                    accessRole: item.accessRole
                });
            });
            this.logger.log(`Retrieved ${calendars.length} calendars for user ${userId}`);
            return calendars;
        }
        catch (error) {
            this.logger.error(`Failed to get calendars for user ${userId}`, error.stack);
            throw new Error(`Failed to retrieve calendars: ${error.message}`);
        }
    }
    /**
     * Connect user to a specific Google calendar.
     * Uses a Redis-based distributed lock so that only one instance
     * can run the connect flow for a given user at a time.
     */
    async connectCalendar(userId, calendarId) {
        const lockKey = `gcal-connect-lock:${userId}`;
        const lockAcquired = await this.redisService.setLock(lockKey, 'locked', 30); // 30-second TTL
        if (!lockAcquired) {
            this.logger.warn(`Connection operation already in progress for user ${userId}`);
            throw new common_1.ConflictException('Google Calendar connection already in progress. Please retry shortly.');
        }
        try {
            return await this._performConnection(userId, calendarId);
        }
        finally {
            await this.redisService.releaseLock(lockKey);
        }
    }
    /**
     * Internal method to perform the actual connection logic
     */
    async _performConnection(userId, calendarId) {
        const user = await this.getUserWithTokens(userId);
        const calendar = await this.getAuthenticatedCalendarClient(user);
        try {
            // Verify calendar exists and user has access
            const calendarResponse = await this.withRetry(() => calendar.calendars.get({
                calendarId
            }));
            const calendarName = calendarResponse.data.summary || 'Unknown Calendar';
            // Update user with selected calendar in a transaction
            await this.userRepository.manager.transaction(async (transactionManager) => {
                await transactionManager.update(user_entity_1.User, userId, {
                    googleCalendarId: calendarId,
                    isGoogleSyncEnabled: true,
                    googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.SUCCESS,
                    googleSyncErrorMessage: undefined,
                    lastGoogleSyncAt: new Date()
                });
            });
            // Initiate webhook for push notifications
            await this.watchCalendar(userId, calendarId);
            // Trigger an initial full sync to populate local cache
            try {
                await this.performIncrementalSync(userId);
            }
            catch (syncErr) {
                this.logger.warn(`Initial full sync failed for user ${userId}: ${syncErr instanceof Error ? syncErr.message : syncErr}`);
            }
            this.logger.log(`User ${userId} connected to calendar: ${calendarName} (${calendarId})`);
            return { calendarName };
        }
        catch (error) {
            this.logger.error(`Failed to connect calendar for user ${userId}`, error.stack);
            await this.userRepository.update(userId, {
                googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.FAILED,
                googleSyncErrorMessage: error.message
            });
            throw new Error(`Failed to connect calendar: ${error.message}`);
        }
    }
    /**
     * Get user's Google Calendar connection status
     */
    async getConnectionStatus(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId }
        });
        if (!user) {
            throw new Error('User not found');
        }
        const isConnected = !!(user.googleCalendarRefreshToken && user.isGoogleSyncEnabled);
        let calendarName;
        if (isConnected && user.googleCalendarId) {
            try {
                const calendar = await this.getAuthenticatedCalendarClient(user);
                const calendarResponse = await calendar.calendars.get({
                    calendarId: user.googleCalendarId
                });
                calendarName = calendarResponse.data.summary || undefined;
            }
            catch (error) {
                this.logger.warn(`Failed to get calendar name for user ${userId}`, error.message);
            }
        }
        return {
            isConnected,
            calendarId: user.googleCalendarId || undefined,
            calendarName,
            syncStatus: user.googleSyncStatus || undefined,
            lastSyncAt: user.lastGoogleSyncAt || undefined,
            errorMessage: user.googleSyncErrorMessage || undefined
        };
    }
    /**
     * Disconnect user from Google Calendar
     */
    async disconnectCalendar(userId) {
        const user = await this.getUserWithTokens(userId);
        // Stop existing webhook channel before disconnecting
        if (user.googleWebhookId && user.googleWebhookResourceId) {
            await this.stopWatchChannel(user);
        }
        // Clear cached events
        await this.cacheService.clearUserEvents(userId);
        // Purge all Google Event IDs from user's appointments
        await this.purgeGoogleEventIdsForUser(user);
        try {
            // Revoke Google tokens if possible
            if (user.googleCalendarRefreshToken) {
                try {
                    const refreshToken = this.encryptionService.decrypt(user.googleCalendarRefreshToken);
                    this.oauth2Client.setCredentials({
                        refresh_token: refreshToken
                    });
                    await this.oauth2Client.revokeCredentials();
                }
                catch (error) {
                    this.logger.warn(`Failed to revoke Google tokens for user ${userId}`, error.message);
                }
            }
            // Clear all Google Calendar data
            await this.userRepository.update(userId, {
                googleCalendarRefreshToken: undefined,
                googleCalendarId: undefined,
                googleSyncToken: undefined,
                isGoogleSyncEnabled: false,
                googleSyncStatus: undefined,
                googleSyncErrorMessage: undefined,
                lastGoogleSyncAt: undefined,
                googleWebhookToken: undefined // also clear webhook token
            });
            this.logger.log(`Cleared Google Calendar data for user ${userId}`);
        }
        catch (error) {
            this.logger.error(`Failed to disconnect user ${userId} from Google Calendar`, error.stack);
            throw new Error(`Failed to disconnect: ${error.message}`);
        }
    }
    /**
     * Get user with Google Calendar tokens
     */
    async getUserWithTokens(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            select: [
                'id',
                'email',
                'firstName',
                'lastName',
                'isGoogleSyncEnabled',
                'googleCalendarRefreshToken',
                'googleCalendarId',
                'googleSyncStatus',
                'lastGoogleSyncAt',
                'googleWebhookToken' // newly added for validation
            ]
        });
        if (!user) {
            throw new Error('User not found');
        }
        return user;
    }
    /**
     * Get authenticated Google Calendar client for user
     */
    async getAuthenticatedCalendarClient(user) {
        if (!user.googleCalendarRefreshToken) {
            throw new common_1.UnauthorizedException('User not connected to Google Calendar');
        }
        try {
            const refreshToken = this.encryptionService.decrypt(user.googleCalendarRefreshToken);
            this.oauth2Client.setCredentials({
                refresh_token: refreshToken
            });
            // Refresh access token if needed
            const tokenResponse = await this.oauth2Client.getAccessToken();
            const calendarClient = googleapis_1.google.calendar({
                version: 'v3',
                auth: this.oauth2Client
            });
            return calendarClient;
        }
        catch (error) {
            this.logger.error(`Failed to authenticate calendar client for user ${user.id}:`, {
                error: error.message,
                errorCode: error.code,
                userId: user.id
            });
            throw new common_1.UnauthorizedException(`Authentication failed: ${error.message}`);
        }
    }
    /**
     * Create a watch channel on a calendar to get push notifications
     */
    async watchCalendar(userId, calendarId) {
        const lockKey = `watch-${userId}`;
        // Prevent concurrent watch operations for the same user
        if (this.connectionLocks.has(lockKey)) {
            this.logger.warn(`Watch operation already in progress for user ${userId}, skipping duplicate`);
            return;
        }
        const watchPromise = this._performWatchSetup(userId, calendarId);
        this.connectionLocks.set(lockKey, watchPromise);
        try {
            await watchPromise;
        }
        finally {
            this.connectionLocks.delete(lockKey);
        }
    }
    /**
     * Internal method to perform watch channel setup
     */
    async _performWatchSetup(userId, calendarId) {
        const user = await this.getUserWithTokens(userId);
        const calendar = await this.getAuthenticatedCalendarClient(user);
        const webhookUrl = this.configService.get('google.calendar.webhookUrl');
        if (!webhookUrl) {
            this.logger.warn(`Google webhook URL is not configured. Skipping watch setup for user ${userId}.`);
            return;
        }
        try {
            // If a channel already exists, stop it first and wait for completion
            if (user.googleWebhookId && user.googleWebhookResourceId) {
                this.logger.log(`Stopping existing watch channel ${user.googleWebhookId} for user ${userId}`);
                await this.stopWatchChannel(user);
                // Refetch user to get updated webhook fields
                const updatedUser = await this.getUserWithTokens(userId);
                if (updatedUser.googleWebhookId) {
                    this.logger.warn(`Previous channel not fully cleaned up for user ${userId}, forcing cleanup`);
                    await this.userRepository.update(userId, {
                        googleWebhookId: undefined,
                        googleWebhookResourceId: undefined
                    });
                }
            }
            const channelId = (0, uuid_1.v4)();
            const webhookToken = (0, uuid_1.v4)(); // Generate a secure token
            this.logger.log(`Creating new watch channel ${channelId} for user ${userId} on calendar ${calendarId}`);
            const ttlSeconds = 604800; // 7 days
            const response = await this.withRetry(() => calendar.events.watch({
                calendarId,
                requestBody: {
                    id: channelId,
                    type: 'web_hook',
                    address: webhookUrl,
                    params: {
                        ttl: ttlSeconds.toString()
                    },
                    token: webhookToken // Include the secure token
                }
            }));
            // Parse expiration (ms epoch string) returned by Google
            let expiresAt;
            if (response.data.expiration) {
                const expNum = Number(response.data.expiration);
                if (!isNaN(expNum)) {
                    expiresAt = new Date(expNum);
                }
            }
            // Update user with new webhook info in a transaction
            await this.userRepository.manager.transaction(async (transactionManager) => {
                await transactionManager.update(user_entity_1.User, userId, {
                    googleWebhookId: response.data.id || undefined,
                    googleWebhookResourceId: response.data.resourceId || undefined,
                    googleWebhookExpiresAt: expiresAt,
                    // Store the secure token encrypted to reduce exposure risk
                    googleWebhookToken: this.encryptionService.encrypt(webhookToken)
                });
            });
            this.logger.log(`Successfully created watch channel ${response.data.id} for user ${userId}`);
        }
        catch (error) {
            this.logger.error(`Failed to create watch channel for user ${userId}`, error.stack);
            await this.userRepository.update(userId, {
                googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.FAILED,
                googleSyncErrorMessage: `Failed to create webhook: ${error.message}`
            });
            throw error;
        }
    }
    /**
     * Stop a watch channel
     */
    async stopWatchChannel(user) {
        if (!user.googleWebhookId || !user.googleWebhookResourceId) {
            this.logger.warn(`User ${user.id} has no webhook to stop.`);
            return;
        }
        const calendar = await this.getAuthenticatedCalendarClient(user);
        try {
            await this.withRetry(() => calendar.channels.stop({
                requestBody: {
                    id: user.googleWebhookId,
                    resourceId: user.googleWebhookResourceId
                }
            }));
            this.logger.log(`Successfully stopped watch channel ${user.googleWebhookId} for user ${user.id}`);
            // Clear the webhook IDs from the user entity after successful stop
            await this.userRepository.update(user.id, {
                googleWebhookId: undefined,
                googleWebhookResourceId: undefined
            });
        }
        catch (error) {
            // A 404 error means the channel doesn't exist anymore, which is fine.
            if (error.code === 404) {
                this.logger.warn(`Watch channel ${user.googleWebhookId} not found on Google's side for user ${user.id}. Clearing from DB.`);
                // Clear the orphaned webhook IDs from the user entity
                await this.userRepository.update(user.id, {
                    googleWebhookId: undefined,
                    googleWebhookResourceId: undefined
                });
            }
            else {
                this.logger.error(`Failed to stop watch channel for user ${user.id}`, error.stack);
                // We still clear the IDs from our side to avoid orphaned webhooks
            }
        }
    }
    /**
     * Process an incoming webhook notification from Google
     * @param channelId - The ID of the watch channel.
     * @param resourceId - The opaque ID of the resource being watched.
     * @param resourceState - The state of the resource (e.g., "sync", "exists", "not_exists").
     */
    async processWebhookNotification(channelId, resourceId, resourceState) {
        if (resourceState === 'sync') {
            // This is a sync request, not a real notification.
            // We may need to re-sync everything if we get this.
            const user = await this.userRepository.findOne({
                where: { googleWebhookId: channelId }
            });
            if (user) {
                // Optional: Trigger a full re-sync here
            }
            return;
        }
        if (resourceState === 'not_exists') {
            return;
        }
        // Find user by channel ID
        const user = await this.userRepository.findOne({
            where: {
                googleWebhookId: channelId,
                googleWebhookResourceId: resourceId
            }
        });
        if (!user) {
            // Also try finding by channelId only
            const userByChannelId = await this.userRepository.findOne({
                where: { googleWebhookId: channelId }
            });
            if (userByChannelId) {
                this.logger.warn(`Webhook found user by channelId only, but resourceId mismatch. Expected: ${resourceId}, Found: ${userByChannelId.googleWebhookResourceId}`);
            }
            return;
        }
        try {
            // Enqueue a job to perform incremental sync for this user.
            await this.sqsService.sendMessage({
                queueKey: 'NidanaGoogleCalendarSync',
                messageBody: {
                    data: {
                        type: 'WEBHOOK_NOTIFICATION',
                        userId: user.id,
                        timestamp: new Date().toISOString(),
                        webhookData: {
                            channelId,
                            resourceId,
                            resourceState
                        }
                    }
                },
                deduplicationId: `webhook-${channelId}-${resourceId}-${Date.now()}`
            });
        }
        catch (error) {
            this.logger.error(`Failed to queue SQS message for user ${user.id}:`, error);
        }
    }
    /**
     * Perform incremental sync with Google Calendar for a user
     */
    async performIncrementalSync(userId) {
        var _a, _b, _c, _d, _e, _f;
        const user = await this.getUserWithTokens(userId);
        const calendar = await this.getAuthenticatedCalendarClient(user);
        let syncToken = user.googleSyncToken;
        if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
            this.logger.warn(`Sync is not enabled for user ${userId}. isGoogleSyncEnabled: ${user.isGoogleSyncEnabled}, googleCalendarId: ${user.googleCalendarId}`);
            return;
        }
        try {
            const fullSync = !syncToken;
            if (fullSync) {
                // Prepare to fetch one year historical events
                syncToken = undefined;
            }
            let nextPageToken;
            let response;
            const eventsToUpsert = [];
            do {
                response = await this.withRetry(() => calendar.events.list({
                    calendarId: user.googleCalendarId,
                    pageToken: nextPageToken,
                    showDeleted: true,
                    singleEvents: true, // expand recurring events into single instances
                    ...(fullSync
                        ? {
                            timeMin: moment()
                                .subtract(1, 'year')
                                .toISOString()
                        }
                        : { syncToken: syncToken })
                }));
                const events = response.data.items;
                this.logger.log(`Fetched ${(events === null || events === void 0 ? void 0 : events.length) || 0} events from Google Calendar for user ${userId} (page token: ${nextPageToken || 'none'})`);
                if (events && events.length > 0) {
                    for (const event of events) {
                        // Log each event for debugging
                        this.logger.debug(`Processing event: ${event.id} - ${event.summary} (${((_a = event.start) === null || _a === void 0 ? void 0 : _a.dateTime) || ((_b = event.start) === null || _b === void 0 ? void 0 : _b.date)}) - Status: ${event.status}`);
                        // Removed skip for recurring events to ensure they are synced
                        if (event.status === 'cancelled') {
                            // Handle deleted event
                            // Remove from cache if exists
                            await this.cacheService.upsertEvent(userId, {
                                id: event.id,
                                calendarId: user.googleCalendarId,
                                start: {
                                    dateTime: (_c = event.start) === null || _c === void 0 ? void 0 : _c.dateTime,
                                    date: (_d = event.start) === null || _d === void 0 ? void 0 : _d.date
                                },
                                end: {
                                    dateTime: (_e = event.end) === null || _e === void 0 ? void 0 : _e.dateTime,
                                    date: (_f = event.end) === null || _f === void 0 ? void 0 : _f.date
                                },
                                status: 'cancelled'
                            });
                        }
                        else {
                            // collect to upsert later in batch
                            eventsToUpsert.push(event);
                        }
                    }
                }
                nextPageToken = response.data.nextPageToken;
                // After the first page, subsequent requests must not send syncToken
                if (nextPageToken) {
                    syncToken = undefined;
                }
            } while (nextPageToken);
            // save/upsert batch into cache
            if (eventsToUpsert.length > 0) {
                this.logger.log(`Saving ${eventsToUpsert.length} events to cache for user ${userId}`);
                await this.cacheService.saveEvents(userId, eventsToUpsert);
                this.logger.log(`Successfully saved ${eventsToUpsert.length} events to cache for user ${userId}`);
            }
            else {
                this.logger.log(`No events to save for user ${userId}`);
            }
            // Save the new sync token for the next sync
            const newSyncToken = response.data.nextSyncToken;
            if (newSyncToken) {
                await this.userRepository.update(userId, {
                    googleSyncToken: newSyncToken,
                    lastGoogleSyncAt: new Date(),
                    googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.SUCCESS,
                    googleSyncErrorMessage: undefined
                });
                this.logger.log(`Incremental sync completed for user ${userId}. New sync token stored.`);
                // Notify front-end via Redis that calendar data changed
                try {
                    await this.redisPubClient.publish('calendar-updates', JSON.stringify({ userId }));
                }
                catch (pubErr) {
                    this.logger.warn(`Failed to publish calendar update for user ${userId}: ${pubErr instanceof Error ? pubErr.message : pubErr}`);
                }
            }
        }
        catch (error) {
            if (error.code === 410) {
                // A 410 error indicates the sync token is invalid.
                this.logger.warn(`Sync token for user ${userId} is invalid. Clearing token to trigger full sync.`);
                await this.userRepository.update(userId, {
                    googleSyncToken: undefined,
                    googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.FAILED,
                    googleSyncErrorMessage: 'Sync token became invalid; a full sync is required.'
                });
                // Optional: Trigger a full sync here
            }
            else {
                this.logger.error(`Incremental sync failed for user ${userId}`, error.stack);
                await this.userRepository.update(userId, {
                    googleSyncStatus: google_sync_status_enum_1.GoogleSyncStatus.FAILED,
                    googleSyncErrorMessage: `Sync failed: ${error.message}`
                });
            }
        }
    }
    /**
     * Creates an event on Google Calendar.
     */
    async createEvent(userId, appointment) {
        try {
            const user = await this.getUserWithTokens(userId);
            if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
                return null;
            }
            const calendar = await this.getAuthenticatedCalendarClient(user);
            const eventDetails = await this.buildEventDetails(appointment);
            const response = await this.withRetry(() => calendar.events.insert({
                calendarId: user.googleCalendarId,
                requestBody: eventDetails
            }));
            if (response.data.id) {
                return response.data.id;
            }
            else {
                return null;
            }
        }
        catch (error) {
            this.logger.error(`Failed to create Google Calendar event for appointment ${appointment.id}:`, {
                error: error.message,
                errorCode: error.code,
                errorStatus: error.status,
                userId,
                appointmentId: appointment.id
            });
            // Check for specific error types
            if (error.code === 401 || error.status === 401) {
                this.logger.error(`Authentication error - user may need to reconnect Google Calendar`);
            }
            else if (error.code === 403 || error.status === 403) {
                this.logger.error(`Permission error - check calendar access permissions`);
            }
            else if (error.code === 429 || error.status === 429) {
                this.logger.error(`Rate limit exceeded - should retry later`);
            }
            return null;
        }
    }
    /**
     * Updates an event on Google Calendar.
     */
    async updateEvent(userId, appointment) {
        if (!appointment.googleEventId) {
            return this.createEvent(userId, appointment);
        }
        const user = await this.getUserWithTokens(userId);
        if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
            return null;
        }
        const calendar = await this.getAuthenticatedCalendarClient(user);
        const eventDetails = await this.buildEventDetails(appointment);
        try {
            const response = await this.withRetry(() => calendar.events.update({
                calendarId: user.googleCalendarId,
                eventId: appointment.googleEventId,
                requestBody: eventDetails
            }));
            this.logger.log(`Updated Google Calendar event ${response.data.id} for appointment ${appointment.id}`);
            return response.data.id || null;
        }
        catch (error) {
            this.logger.error(`Failed to update Google Calendar event for appointment ${appointment.id}`, error);
            // Optional: Queue for retry
            return null;
        }
    }
    /**
     * Deletes an event from Google Calendar.
     */
    async deleteEvent(userId, googleEventId) {
        const user = await this.getUserWithTokens(userId);
        if (!user.isGoogleSyncEnabled || !user.googleCalendarId) {
            return;
        }
        const calendar = await this.getAuthenticatedCalendarClient(user);
        try {
            await this.withRetry(() => calendar.events.delete({
                calendarId: user.googleCalendarId,
                eventId: googleEventId
            }));
            this.logger.log(`Deleted Google Calendar event ${googleEventId}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete Google Calendar event ${googleEventId}`, error);
            // Optional: Queue for retry
        }
    }
    /**
     * Get events from Google Calendar for a specific date range
     */
    async getEventsForDate(userId, _calendarId, startDate, endDate) {
        // Return cached events within range for the user.
        try {
            const cached = await this.cacheService.getEventsRange(userId, startDate, endDate);
            this.logger.log(`Retrieved ${cached.length} cached events for user ${userId} between ${startDate.toISOString()} and ${endDate.toISOString()}`);
            return cached.map(c => {
                var _a;
                return (_a = c.raw) !== null && _a !== void 0 ? _a : {
                    id: c.eventId,
                    summary: c.summary,
                    description: c.description,
                    start: { dateTime: c.startTime.toISOString() },
                    end: { dateTime: c.endTime.toISOString() },
                    status: c.status
                };
            });
        }
        catch (error) {
            this.logger.error(`Failed to read cached events for user ${userId}`, error);
            return [];
        }
    }
    /**
     * Debug method to get detailed sync information
     */
    async getDebugSyncInfo(userId) {
        try {
            const user = await this.userRepository.findOne({
                where: { id: userId }
            });
            if (!user) {
                return { error: 'User not found' };
            }
            // Get cached events count
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const cachedEvents = await this.cacheService.getEventsRange(userId, today, tomorrow);
            return {
                user: {
                    id: user.id,
                    email: user.email,
                    isGoogleSyncEnabled: user.isGoogleSyncEnabled,
                    googleCalendarId: user.googleCalendarId,
                    googleSyncStatus: user.googleSyncStatus,
                    googleSyncErrorMessage: user.googleSyncErrorMessage,
                    lastGoogleSyncAt: user.lastGoogleSyncAt,
                    googleWebhookId: user.googleWebhookId,
                    googleWebhookExpiresAt: user.googleWebhookExpiresAt
                },
                cachedEventsToday: {
                    count: cachedEvents.length,
                    events: cachedEvents.map(e => ({
                        eventId: e.eventId,
                        summary: e.summary,
                        startTime: e.startTime,
                        endTime: e.endTime,
                        status: e.status
                    }))
                }
            };
        }
        catch (error) {
            this.logger.error(`Failed to get debug sync info for user ${userId}`, error);
            return { error: error.message };
        }
    }
    async resolveBrandSlug(appointment) {
        var _a, _b, _c;
        // Attempt 1: direct relation if loaded
        let slug = (_b = (_a = appointment.clinic) === null || _a === void 0 ? void 0 : _a.brand) === null || _b === void 0 ? void 0 : _b.slug;
        if (slug) {
            return slug;
        }
        // Attempt 2: fetch via brandId (from appointment or clinic)
        const brandId = appointment.brandId || ((_c = appointment.clinic) === null || _c === void 0 ? void 0 : _c.brandId);
        if (!brandId)
            return undefined;
        try {
            const brand = await this.brandRepository.findOne({
                where: { id: brandId }
            });
            return brand === null || brand === void 0 ? void 0 : brand.slug;
        }
        catch (err) {
            this.logger.warn(`Failed to resolve brand slug for brandId ${brandId}: ${err instanceof Error ? err.message : err}`);
            return undefined;
        }
    }
    async buildEventDetails(appointment) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        // Derive patient and clinic names if available, fallback to IDs otherwise
        const patientName = ((_a = appointment.patient) === null || _a === void 0 ? void 0 : _a.patientName) || appointment.patientId;
        const clinicName = ((_b = appointment.clinic) === null || _b === void 0 ? void 0 : _b.name) || appointment.clinicId;
        // Extract primary (or first) owner details if they are loaded
        let ownerName;
        let ownerPhone;
        const owners = (_c = appointment.patient) === null || _c === void 0 ? void 0 : _c.patientOwners;
        if (owners && owners.length > 0) {
            // Prefer explicitly marked primary owner, otherwise take the first
            const primaryOwner = (_d = owners.find(o => o.isPrimary)) !== null && _d !== void 0 ? _d : owners[0];
            const ownerBrand = primaryOwner === null || primaryOwner === void 0 ? void 0 : primaryOwner.ownerBrand;
            if (ownerBrand) {
                ownerName =
                    `${(_e = ownerBrand.firstName) !== null && _e !== void 0 ? _e : ''} ${(_f = ownerBrand.lastName) !== null && _f !== void 0 ? _f : ''}`.trim();
                ownerPhone = (_g = ownerBrand.globalOwner) === null || _g === void 0 ? void 0 : _g.phoneNumber;
            }
        }
        // Determine the correct brand slug (relation may or may not be loaded)
        const brandSlug = await this.resolveBrandSlug(appointment);
        const baseAdminUrl = (0, get_login_url_1.getAdminPortalUrl)(brandSlug);
        const nidanaLink = `${baseAdminUrl}/patients/${appointment.patientId}/details`;
        // Build event description with optional owner details
        const descriptionLines = [
            'Appointment Details:',
            `- Reason: ${appointment.reason || ''}`,
            `- Patient: ${patientName}`
        ];
        if (ownerName) {
            descriptionLines.push(`- Owner: ${ownerName}${ownerPhone ? ` (${ownerPhone})` : ''}`);
        }
        descriptionLines.push(`- Clinic: ${clinicName}`);
        descriptionLines.push(`- View in Nidana: ${nidanaLink}`);
        const description = descriptionLines.join('\n');
        // Handle both Date objects and ISO strings for start/end times
        const startDateTime = appointment.startTime instanceof Date
            ? appointment.startTime.toISOString()
            : appointment.startTime;
        const endDateTime = appointment.endTime instanceof Date
            ? appointment.endTime.toISOString()
            : appointment.endTime;
        // Determine clinic timezone
        const clinicTz = ((_h = appointment.clinic) === null || _h === void 0 ? void 0 : _h.timezone) || 'UTC';
        // Provide a meaningful title for the event (include patient name)
        const baseTitle = appointment.type || ((_j = appointment.reason) === null || _j === void 0 ? void 0 : _j.trim()) || 'Appointment';
        const eventTitle = `${patientName} - ${baseTitle}`;
        // const eventTitle = clinicName
        // 	? `${clinicName}: ${patientName} - ${baseTitle}`
        // 	: `${patientName} - ${baseTitle}`;
        return {
            summary: eventTitle,
            description,
            start: {
                dateTime: startDateTime,
                timeZone: clinicTz
            },
            end: {
                dateTime: endDateTime,
                timeZone: clinicTz
            },
            // attendees: [{ email: '<EMAIL>' }], // Future enhancement
            extendedProperties: {
                private: {
                    nidanaAppointmentId: appointment.id
                }
            }
        };
    }
    /**
     * Purges all Google Event IDs from appointments associated with a user.
     */
    async purgeGoogleEventIdsForUser(user) {
        if (!user.mobileNumber) {
            this.logger.warn(`User ${user.id} has no mobile number, cannot purge event IDs.`);
            return;
        }
        try {
            const appointmentRepository = this.userRepository.manager.getRepository(appointment_entity_1.AppointmentEntity);
            // Find all appointment IDs that need to be purged
            const appointmentsToUpdate = await appointmentRepository
                .createQueryBuilder('appointment')
                .innerJoin(patient_owner_entity_1.PatientOwner, 'po', 'po.patientId = appointment.patientId')
                .innerJoin(owner_brand_entity_1.OwnerBrand, 'ob', 'ob.id = po.ownerId')
                .innerJoin(global_owner_entity_1.GlobalOwner, 'go', 'go.id = ob.globalOwnerId')
                .where('go.phoneNumber = :phoneNumber', {
                phoneNumber: user.mobileNumber
            })
                .andWhere('appointment.googleEventId IS NOT NULL')
                .select('appointment.id')
                .getMany();
            if (appointmentsToUpdate.length > 0) {
                const appointmentIds = appointmentsToUpdate.map(a => a.id);
                await appointmentRepository.update(appointmentIds, {
                    googleEventId: undefined
                });
                this.logger.log(`Purged Google Event IDs from ${appointmentIds.length} appointments for user ${user.id}.`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to purge Google Event IDs for user ${user.id}`, error.stack);
            // Do not re-throw, as failing to purge event IDs should not block disconnection
        }
    }
    /**
     * Cleanup orphaned watch channels (can be called periodically)
     */
    async cleanupOrphanedChannels() {
        var _a;
        this.logger.log('Starting cleanup of orphaned Google Calendar watch channels');
        try {
            // Find all users with webhook IDs but potentially orphaned channels
            const usersWithWebhooks = await this.userRepository.find({
                where: {
                    googleWebhookId: (0, typeorm_3.Not)((0, typeorm_3.IsNull)()),
                    isGoogleSyncEnabled: true
                }
            });
            this.logger.log(`Found ${usersWithWebhooks.length} users with active webhooks to verify`);
            for (const user of usersWithWebhooks) {
                try {
                    // Try to verify if the channel still exists by attempting to stop it
                    // If it fails with "not found", we know it's orphaned
                    if (user.googleWebhookId && user.googleWebhookResourceId) {
                        const calendar = await this.getAuthenticatedCalendarClient(user);
                        // Attempt to stop the channel - this will fail if it doesn't exist
                        await calendar.channels.stop({
                            requestBody: {
                                id: user.googleWebhookId,
                                resourceId: user.googleWebhookResourceId
                            }
                        });
                        this.logger.log(`Channel ${user.googleWebhookId} for user ${user.id} is still active`);
                    }
                }
                catch (error) {
                    // If we get a "not found" error, the channel is orphaned
                    if (((_a = error.message) === null || _a === void 0 ? void 0 : _a.includes('not found')) ||
                        error.status === 404) {
                        this.logger.warn(`Cleaning up orphaned channel ${user.googleWebhookId} for user ${user.id}`);
                        await this.userRepository.update(user.id, {
                            googleWebhookId: undefined,
                            googleWebhookResourceId: undefined
                        });
                    }
                    else {
                        this.logger.warn(`Could not verify channel ${user.googleWebhookId} for user ${user.id}: ${error.message}`);
                    }
                }
            }
            this.logger.log('Completed cleanup of orphaned Google Calendar watch channels');
        }
        catch (error) {
            this.logger.error('Failed to cleanup orphaned channels', error.stack);
        }
    }
    /**
     * Daily cron job to renew channels expiring within 24 hours.
     */
    async renewExpiringChannels() {
        this.logger.log('Starting renewal check for expiring Google Calendar channels');
        const now = new Date();
        const cutoff = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        try {
            const expiringUsers = await this.userRepository.find({
                where: {
                    isGoogleSyncEnabled: true,
                    googleWebhookExpiresAt: (0, typeorm_3.Not)((0, typeorm_3.IsNull)())
                }
            });
            for (const user of expiringUsers) {
                if (user.googleWebhookExpiresAt &&
                    user.googleWebhookExpiresAt <= cutoff) {
                    this.logger.log(`Renewing channel for user ${user.id}`);
                    if (user.googleCalendarId) {
                        try {
                            await this.watchCalendar(user.id, user.googleCalendarId);
                        }
                        catch (err) {
                            this.logger.error(`Failed to renew channel for user ${user.id}`, err);
                        }
                    }
                }
            }
        }
        catch (err) {
            this.logger.error('Error during channel renewal job', err);
        }
    }
};
exports.GoogleCalendarService = GoogleCalendarService;
__decorate([
    (0, schedule_1.Cron)('0 3 * * *') // 03:00 AM daily
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GoogleCalendarService.prototype, "renewExpiringChannels", null);
exports.GoogleCalendarService = GoogleCalendarService = GoogleCalendarService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => sqs_service_1.SqsService))),
    __param(6, (0, typeorm_1.InjectRepository)(brand_entity_1.Brand)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        config_1.ConfigService,
        encryption_service_1.EncryptionService,
        sqs_service_1.SqsService,
        google_calendar_cache_service_1.GoogleCalendarCacheService,
        redis_service_1.RedisService,
        typeorm_2.Repository,
        google_calendar_error_handler_1.GoogleCalendarErrorHandler])
], GoogleCalendarService);
//# sourceMappingURL=google-calendar.service.js.map